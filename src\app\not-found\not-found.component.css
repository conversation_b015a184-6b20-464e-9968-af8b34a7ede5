/* 404 Not Found Page Styles - Benedicto College Library Management System */
/* Advanced Tailwind CSS with Floating Particles and Stunning Animations */

/* Enhanced mobile menu button states */
#hamburger-icon.hidden {
  opacity: 0;
  transform: rotate(180deg);
}

#close-icon.show {
  opacity: 1;
  transform: rotate(0deg);
}

#close-icon {
  opacity: 0;
  transform: rotate(-180deg);
  transition: all 0.3s ease;
}

#hamburger-icon {
  transition: all 0.3s ease;
}

/* ===== FLOATING PARTICLES SYSTEM ===== */

/* Large floating particles */
.particle {
  position: absolute;
  background: linear-gradient(45deg, rgba(168, 85, 247, 0.4), rgba(236, 72, 153, 0.4));
  border-radius: 50%;
  pointer-events: none;
  animation: float 20s infinite linear;
}

.particle-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 25s;
}

.particle-2 {
  width: 60px;
  height: 60px;
  top: 20%;
  right: 15%;
  animation-delay: -5s;
  animation-duration: 30s;
}

.particle-3 {
  width: 100px;
  height: 100px;
  top: 60%;
  left: 5%;
  animation-delay: -10s;
  animation-duration: 35s;
}

.particle-4 {
  width: 40px;
  height: 40px;
  top: 80%;
  right: 20%;
  animation-delay: -15s;
  animation-duration: 20s;
}

.particle-5 {
  width: 70px;
  height: 70px;
  top: 40%;
  right: 5%;
  animation-delay: -20s;
  animation-duration: 28s;
}

.particle-6 {
  width: 50px;
  height: 50px;
  top: 70%;
  left: 80%;
  animation-delay: -8s;
  animation-duration: 32s;
}

.particle-7 {
  width: 90px;
  height: 90px;
  top: 30%;
  left: 70%;
  animation-delay: -12s;
  animation-duration: 26s;
}

.particle-8 {
  width: 35px;
  height: 35px;
  top: 90%;
  left: 40%;
  animation-delay: -18s;
  animation-duration: 22s;
}

.particle-9 {
  width: 65px;
  height: 65px;
  top: 15%;
  left: 50%;
  animation-delay: -3s;
  animation-duration: 29s;
}

.particle-10 {
  width: 45px;
  height: 45px;
  top: 50%;
  left: 20%;
  animation-delay: -25s;
  animation-duration: 24s;
}

/* Small floating dots */
.dot {
  position: absolute;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.6), rgba(147, 51, 234, 0.6));
  border-radius: 50%;
  pointer-events: none;
  animation: float 15s infinite linear;
}

.dot-1 { width: 8px; height: 8px; top: 25%; left: 15%; animation-delay: 0s; }
.dot-2 { width: 6px; height: 6px; top: 35%; right: 25%; animation-delay: -2s; }
.dot-3 { width: 10px; height: 10px; top: 55%; left: 85%; animation-delay: -4s; }
.dot-4 { width: 7px; height: 7px; top: 75%; right: 15%; animation-delay: -6s; }
.dot-5 { width: 9px; height: 9px; top: 45%; left: 25%; animation-delay: -8s; }
.dot-6 { width: 5px; height: 5px; top: 65%; right: 35%; animation-delay: -10s; }
.dot-7 { width: 8px; height: 8px; top: 85%; left: 65%; animation-delay: -12s; }
.dot-8 { width: 6px; height: 6px; top: 15%; right: 45%; animation-delay: -14s; }
.dot-9 { width: 7px; height: 7px; top: 95%; left: 35%; animation-delay: -16s; }
.dot-10 { width: 9px; height: 9px; top: 5%; left: 75%; animation-delay: -18s; }
.dot-11 { width: 5px; height: 5px; top: 40%; right: 55%; animation-delay: -20s; }
.dot-12 { width: 8px; height: 8px; top: 60%; left: 45%; animation-delay: -22s; }
.dot-13 { width: 6px; height: 6px; top: 80%; right: 65%; animation-delay: -24s; }
.dot-14 { width: 7px; height: 7px; top: 20%; left: 55%; animation-delay: -26s; }
.dot-15 { width: 10px; height: 10px; top: 70%; right: 75%; animation-delay: -28s; }

/* Geometric shapes */
.geometric-shape {
  position: absolute;
  pointer-events: none;
  animation: rotate-float 40s infinite linear;
}

.shape-1 {
  width: 30px;
  height: 30px;
  background: linear-gradient(45deg, rgba(34, 197, 94, 0.3), rgba(59, 130, 246, 0.3));
  transform: rotate(45deg);
  top: 30%;
  left: 80%;
  animation-delay: 0s;
}

.shape-2 {
  width: 25px;
  height: 25px;
  background: linear-gradient(45deg, rgba(239, 68, 68, 0.3), rgba(245, 101, 101, 0.3));
  border-radius: 50% 0 50% 0;
  top: 70%;
  right: 10%;
  animation-delay: -10s;
}

.shape-3 {
  width: 35px;
  height: 35px;
  background: linear-gradient(45deg, rgba(168, 85, 247, 0.3), rgba(236, 72, 153, 0.3));
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  top: 20%;
  left: 30%;
  animation-delay: -20s;
}

.shape-4 {
  width: 28px;
  height: 28px;
  background: linear-gradient(45deg, rgba(251, 191, 36, 0.3), rgba(245, 158, 11, 0.3));
  transform: rotate(45deg);
  top: 80%;
  left: 70%;
  animation-delay: -30s;
}

.shape-5 {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, rgba(20, 184, 166, 0.3), rgba(6, 182, 212, 0.3));
  border-radius: 0 50% 0 50%;
  top: 50%;
  right: 30%;
  animation-delay: -15s;
}

/* Button hover effects */
button:hover {
  transform: translateY(-2px);
}

/* Quick links hover effects */
.grid a:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
}

.grid a:hover svg {
  color: #2563eb;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .text-6xl {
    font-size: 4rem;
  }
  
  .text-8xl {
    font-size: 5rem;
  }
  
  .text-9xl {
    font-size: 6rem;
  }
}

/* Mobile navigation pane width adjustment for smaller screens */
@media (max-width: 480px) {
  #nav-links {
    width: 100vw;
    max-width: 320px;
  }
}

/* Enhanced focus states for accessibility */
button:focus,
a:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Logo fade-in animation */
.logo-fade-in {
  animation: fadeIn 1s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Error message slide-in animation */
.error-slide-in {
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button group animation */
.button-group {
  animation: slideInUp 1s ease-out 0.3s both;
}

/* Quick links animation */
.quick-links {
  animation: slideInUp 1.2s ease-out 0.5s both;
}
