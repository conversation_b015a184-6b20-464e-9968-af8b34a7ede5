<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white flex flex-col overflow-hidden relative">

  <!-- Floating Particles Background -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <!-- Large floating particles -->
    <div class="particle particle-1"></div>
    <div class="particle particle-2"></div>
    <div class="particle particle-3"></div>
    <div class="particle particle-4"></div>
    <div class="particle particle-5"></div>
    <div class="particle particle-6"></div>
    <div class="particle particle-7"></div>
    <div class="particle particle-8"></div>
    <div class="particle particle-9"></div>
    <div class="particle particle-10"></div>

    <!-- Small floating dots -->
    <div class="dot dot-1"></div>
    <div class="dot dot-2"></div>
    <div class="dot dot-3"></div>
    <div class="dot dot-4"></div>
    <div class="dot dot-5"></div>
    <div class="dot dot-6"></div>
    <div class="dot dot-7"></div>
    <div class="dot dot-8"></div>
    <div class="dot dot-9"></div>
    <div class="dot dot-10"></div>
    <div class="dot dot-11"></div>
    <div class="dot dot-12"></div>
    <div class="dot dot-13"></div>
    <div class="dot dot-14"></div>
    <div class="dot dot-15"></div>

    <!-- Geometric shapes -->
    <div class="geometric-shape shape-1"></div>
    <div class="geometric-shape shape-2"></div>
    <div class="geometric-shape shape-3"></div>
    <div class="geometric-shape shape-4"></div>
    <div class="geometric-shape shape-5"></div>
  </div>

  <!-- Gradient overlay for depth -->
  <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent pointer-events-none"></div>
  
  <!-- Enhanced Professional Header with Glass Morphism -->
  <header class="bg-white/10 backdrop-blur-md border-b border-white/20 shadow-2xl relative z-50 w-full overflow-x-hidden">
    <div class="container mx-auto px-4 sm:px-6 py-2 sm:py-3 max-w-full">
      <!-- Main header row -->
      <div class="flex justify-between items-center w-full min-w-0">
        <!-- Logo section -->
        <div class="flex-shrink-0 flex justify-start items-center min-w-0">
          <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
            <img
              src="assets/images/BcLogo.png"
              alt="Benedicto College Logo"
              class="h-8 sm:h-10 md:h-12 lg:h-14 w-auto max-w-full object-contain"
              onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
              onload="console.log('Logo loaded successfully:', this.src);"
            >
          </a>
        </div>

        <!-- Desktop Navigation - Right aligned with SVG icons only -->
        <nav class="hidden md:flex items-center space-x-1 lg:space-x-2 flex-shrink-0">
          <!-- Account/Login -->
          <div class="group relative">
            <a routerLink="/login" class="flex items-center justify-center p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-purple-500/25">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black/80 backdrop-blur-sm text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap border border-white/20">
              Account
            </div>
          </div>

          <!-- About -->
          <div class="group relative">
            <a routerLink="/about" class="flex items-center justify-center p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-blue-500/25">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black/80 backdrop-blur-sm text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap border border-white/20">
              About
            </div>
          </div>

          <!-- Support -->
          <div class="group relative">
            <a routerLink="/support" class="flex items-center justify-center p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-green-500/25">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black/80 backdrop-blur-sm text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap border border-white/20">
              Help & Support
            </div>
          </div>

          <!-- Contact -->
          <div class="group relative">
            <a routerLink="/contact" class="flex items-center justify-center p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-pink-500/25">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black/80 backdrop-blur-sm text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap border border-white/20">
              Contact
            </div>
          </div>
        </nav>

        <!-- Mobile menu button -->
        <button
          id="mobile-burger"
          (click)="toggleMobileMenu()"
          class="md:hidden flex items-center justify-center p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-110"
          aria-label="Toggle mobile menu"
        >
          <svg id="hamburger-icon" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          <svg id="close-icon" class="w-6 h-6 text-white absolute" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation Pane -->
    <nav id="nav-links" class="md:hidden bg-black/90 backdrop-blur-xl border-l border-white/20">
      <div class="flex flex-col space-y-4 pt-8">
        <!-- Mobile Logo -->
        <div class="flex justify-center mb-6">
          <img src="assets/images/BcLogo.png" alt="Benedicto College" class="h-12 w-auto filter brightness-0 invert">
        </div>

        <!-- Mobile Navigation Links -->
        <a routerLink="/login" class="text-white text-center py-3 px-4 mx-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
          Account
        </a>
        <a routerLink="/about" class="text-white text-center py-3 px-4 mx-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
          About
        </a>
        <a routerLink="/support" class="text-white text-center py-3 px-4 mx-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
          Help & Support
        </a>
        <a routerLink="/contact" class="text-white text-center py-3 px-4 mx-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
          Contact
        </a>
        <a routerLink="/terms-of-service" class="text-white text-center py-3 px-4 mx-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
          Terms
        </a>
        <a routerLink="/privacy-policy" class="text-white text-center py-3 px-4 mx-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
          Privacy Policy
        </a>
      </div>
    </nav>
  </header>

  <!-- Main 404 Content -->
  <main class="flex-1 flex items-center justify-center px-4 py-16 relative z-10">
    <div class="max-w-4xl mx-auto text-center">
      <!-- Benedicto College Logo with Glow Effect -->
      <div class="mb-12 logo-fade-in relative">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 rounded-full blur-3xl opacity-30 animate-pulse"></div>
        <img
          src="assets/images/BcLogo.png"
          alt="Benedicto College Logo"
          class="h-32 sm:h-40 md:h-48 w-auto mx-auto relative z-10 filter brightness-0 invert animate-float"
          onerror="console.error('Logo failed to load:', this.src);"
        >
      </div>

      <!-- 404 Error Message with Gradient Text -->
      <div class="mb-12 error-slide-in">
        <h1 class="text-8xl sm:text-9xl md:text-[12rem] font-black bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent mb-6 animate-pulse-slow leading-none">
          404
        </h1>
        <div class="relative">
          <h2 class="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-6 animate-slide-up">
            Page Not Found
          </h2>
          <div class="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg blur opacity-25 animate-pulse"></div>
        </div>
        <p class="text-xl sm:text-2xl text-gray-300 mb-4 animate-fade-in-delayed">
          Oops! The page you're looking for doesn't exist.
        </p>
        <p class="text-lg sm:text-xl text-gray-400 animate-fade-in-delayed-2">
          It might have been moved, deleted, or you entered the wrong URL.
        </p>
      </div>

      <!-- Benedicto College Context with Glass Morphism -->
      <div class="mb-12 p-8 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl hover:bg-white/15 transition-all duration-500 animate-fade-in-delayed-3 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 animate-gradient-shift"></div>
        <div class="relative z-10">
          <h3 class="text-2xl sm:text-3xl font-bold text-white mb-4 bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent">
            Benedicto College Library Management System
          </h3>
          <p class="text-gray-300 text-lg leading-relaxed">
            You're currently browsing the digital library portal for Benedicto College.
            Our system helps students, faculty, and staff access library resources efficiently.
          </p>
        </div>
      </div>

      <!-- Action Buttons with Stunning Effects -->
      <div class="flex flex-col sm:flex-row gap-6 justify-center button-group mb-16">
        <button
          (click)="goHome()"
          class="group relative px-10 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold rounded-2xl overflow-hidden transition-all duration-500 hover:scale-110 hover:shadow-2xl hover:shadow-purple-500/50 transform-gpu"
        >
          <div class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div class="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-shimmer"></div>
          <div class="relative z-10 flex items-center justify-center">
            <svg class="w-6 h-6 mr-3 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            <span class="text-lg">Go to Homepage</span>
          </div>
        </button>

        <button
          (click)="goBack()"
          class="group relative px-10 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-bold rounded-2xl overflow-hidden transition-all duration-500 hover:scale-110 hover:shadow-2xl hover:shadow-blue-500/50 transform-gpu"
        >
          <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div class="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-shimmer"></div>
          <div class="relative z-10 flex items-center justify-center">
            <svg class="w-6 h-6 mr-3 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            <span class="text-lg">Go Back</span>
          </div>
        </button>
      </div>

      <!-- Quick Links with Glass Cards -->
      <div class="quick-links">
        <h4 class="text-2xl font-bold text-white mb-8 bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent">Quick Links</h4>
        <div class="grid grid-cols-2 sm:grid-cols-4 gap-6">
          <a routerLink="/login" class="group p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl hover:bg-white/20 hover:border-white/40 transition-all duration-500 text-center hover:scale-110 hover:shadow-2xl hover:shadow-purple-500/25 transform-gpu">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
              <svg class="w-8 h-8 mx-auto mb-3 text-purple-300 group-hover:text-white transition-colors duration-300 relative z-10 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-white group-hover:text-purple-200 transition-colors duration-300">Login</span>
          </a>

          <a routerLink="/about" class="group p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl hover:bg-white/20 hover:border-white/40 transition-all duration-500 text-center hover:scale-110 hover:shadow-2xl hover:shadow-blue-500/25 transform-gpu">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
              <svg class="w-8 h-8 mx-auto mb-3 text-blue-300 group-hover:text-white transition-colors duration-300 relative z-10 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-white group-hover:text-blue-200 transition-colors duration-300">About</span>
          </a>

          <a routerLink="/support" class="group p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl hover:bg-white/20 hover:border-white/40 transition-all duration-500 text-center hover:scale-110 hover:shadow-2xl hover:shadow-green-500/25 transform-gpu">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
              <svg class="w-8 h-8 mx-auto mb-3 text-green-300 group-hover:text-white transition-colors duration-300 relative z-10 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-white group-hover:text-green-200 transition-colors duration-300">Support</span>
          </a>

          <a routerLink="/contact" class="group p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl hover:bg-white/20 hover:border-white/40 transition-all duration-500 text-center hover:scale-110 hover:shadow-2xl hover:shadow-pink-500/25 transform-gpu">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-pink-400 to-rose-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
              <svg class="w-8 h-8 mx-auto mb-3 text-pink-300 group-hover:text-white transition-colors duration-300 relative z-10 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-white group-hover:text-pink-200 transition-colors duration-300">Contact</span>
          </a>
        </div>
      </div>
    </div>
  </main>
</div>
