<div class="min-h-screen bg-white text-gray-800 flex flex-col overflow-x-hidden">
  
  <!-- Enhanced Professional Header with Hover Text Labels -->
  <header class="bg-gray-950 shadow-xl relative z-10 w-full overflow-x-hidden">
    <div class="container mx-auto px-4 sm:px-6 py-2 sm:py-3 max-w-full">
      <!-- Main header row -->
      <div class="flex justify-between items-center w-full min-w-0">
        <!-- Logo section -->
        <div class="flex-shrink-0 flex justify-start items-center min-w-0">
          <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
            <img
              src="assets/images/BcLogo.png"
              alt="Benedicto College Logo"
              class="h-8 sm:h-10 md:h-12 lg:h-14 w-auto max-w-full object-contain"
              onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
              onload="console.log('Logo loaded successfully:', this.src);"
            >
          </a>
        </div>

        <!-- Desktop Navigation - Right aligned with SVG icons only -->
        <nav class="hidden md:flex items-center space-x-1 lg:space-x-2 flex-shrink-0">
          <!-- Account/Login -->
          <div class="group relative">
            <a routerLink="/login" class="flex items-center justify-center p-2 rounded-lg transition-all duration-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
              Account
            </div>
          </div>

          <!-- About -->
          <div class="group relative">
            <a routerLink="/about" class="flex items-center justify-center p-2 rounded-lg transition-all duration-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
              About
            </div>
          </div>

          <!-- Support -->
          <div class="group relative">
            <a routerLink="/support" class="flex items-center justify-center p-2 rounded-lg transition-all duration-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
              Help & Support
            </div>
          </div>

          <!-- Contact -->
          <div class="group relative">
            <a routerLink="/contact" class="flex items-center justify-center p-2 rounded-lg transition-all duration-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
              Contact
            </div>
          </div>
        </nav>

        <!-- Mobile menu button -->
        <button
          id="mobile-burger"
          (click)="toggleMobileMenu()"
          class="md:hidden flex items-center justify-center p-2 rounded-lg hover:bg-gray-800 transition-colors duration-300"
          aria-label="Toggle mobile menu"
        >
          <svg id="hamburger-icon" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          <svg id="close-icon" class="w-6 h-6 text-white absolute" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation Pane -->
    <nav id="nav-links" class="md:hidden">
      <div class="flex flex-col space-y-4 pt-8">
        <!-- Mobile Logo -->
        <div class="flex justify-center mb-6">
          <img src="assets/images/BcLogo.png" alt="Benedicto College" class="h-12 w-auto">
        </div>

        <!-- Mobile Navigation Links -->
        <a routerLink="/login" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Account
        </a>
        <a routerLink="/about" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          About
        </a>
        <a routerLink="/support" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Help & Support
        </a>
        <a routerLink="/contact" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Contact
        </a>
        <a routerLink="/terms-of-service" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Terms
        </a>
        <a routerLink="/privacy-policy" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Privacy Policy
        </a>
      </div>
    </nav>
  </header>

  <!-- Main 404 Content -->
  <main class="flex-1 flex items-center justify-center px-4 py-16">
    <div class="max-w-2xl mx-auto text-center">
      <!-- Benedicto College Logo -->
      <div class="mb-8 logo-fade-in">
        <img
          src="assets/images/BcLogo.png"
          alt="Benedicto College Logo"
          class="h-24 sm:h-32 md:h-40 w-auto mx-auto opacity-80 animate-bounce-slow"
          onerror="console.error('Logo failed to load:', this.src);"
        >
      </div>

      <!-- 404 Error Message -->
      <div class="mb-8 error-slide-in">
        <h1 class="text-6xl sm:text-8xl md:text-9xl font-bold text-gray-300 mb-4">404</h1>
        <h2 class="text-2xl sm:text-3xl md:text-4xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
        <p class="text-lg sm:text-xl text-gray-600 mb-2">
          Oops! The page you're looking for doesn't exist.
        </p>
        <p class="text-base sm:text-lg text-gray-500">
          It might have been moved, deleted, or you entered the wrong URL.
        </p>
      </div>

      <!-- Benedicto College Context -->
      <div class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
        <h3 class="text-xl font-semibold text-gray-800 mb-2">Benedicto College Library Management System</h3>
        <p class="text-gray-600">
          You're currently browsing the digital library portal for Benedicto College.
          Our system helps students, faculty, and staff access library resources efficiently.
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center button-group">
        <button
          (click)="goHome()"
          class="px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
          Go to Homepage
        </button>

        <button
          (click)="goBack()"
          class="px-8 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Go Back
        </button>
      </div>

      <!-- Quick Links with Glass Cards -->
      <div class="quick-links">
        <h4 class="text-2xl font-bold text-white mb-8 bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent">Quick Links</h4>
        <div class="grid grid-cols-2 sm:grid-cols-4 gap-6">
          <a routerLink="/login" class="group p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl hover:bg-white/20 hover:border-white/40 transition-all duration-500 text-center hover:scale-110 hover:shadow-2xl hover:shadow-purple-500/25 transform-gpu">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
              <svg class="w-8 h-8 mx-auto mb-3 text-purple-300 group-hover:text-white transition-colors duration-300 relative z-10 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-white group-hover:text-purple-200 transition-colors duration-300">Login</span>
          </a>

          <a routerLink="/about" class="group p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl hover:bg-white/20 hover:border-white/40 transition-all duration-500 text-center hover:scale-110 hover:shadow-2xl hover:shadow-blue-500/25 transform-gpu">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
              <svg class="w-8 h-8 mx-auto mb-3 text-blue-300 group-hover:text-white transition-colors duration-300 relative z-10 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-white group-hover:text-blue-200 transition-colors duration-300">About</span>
          </a>

          <a routerLink="/support" class="group p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl hover:bg-white/20 hover:border-white/40 transition-all duration-500 text-center hover:scale-110 hover:shadow-2xl hover:shadow-green-500/25 transform-gpu">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
              <svg class="w-8 h-8 mx-auto mb-3 text-green-300 group-hover:text-white transition-colors duration-300 relative z-10 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-white group-hover:text-green-200 transition-colors duration-300">Support</span>
          </a>

          <a routerLink="/contact" class="group p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl hover:bg-white/20 hover:border-white/40 transition-all duration-500 text-center hover:scale-110 hover:shadow-2xl hover:shadow-pink-500/25 transform-gpu">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-pink-400 to-rose-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
              <svg class="w-8 h-8 mx-auto mb-3 text-pink-300 group-hover:text-white transition-colors duration-300 relative z-10 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-white group-hover:text-pink-200 transition-colors duration-300">Contact</span>
          </a>
        </div>
      </div>
    </div>
  </main>
</div>
