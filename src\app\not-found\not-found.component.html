<div class="min-h-screen bg-white text-gray-800 flex flex-col overflow-x-hidden">
  
  <!-- Enhanced Professional Header with Hover Text Labels -->
  <header class="bg-gray-950 shadow-xl relative z-10 w-full overflow-x-hidden">
    <div class="container mx-auto px-4 sm:px-6 py-2 sm:py-3 max-w-full">
      <!-- Main header row -->
      <div class="flex justify-between items-center w-full min-w-0">
        <!-- Logo section -->
        <div class="flex-shrink-0 flex justify-start items-center min-w-0">
          <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
            <img
              src="assets/images/BcLogo.png"
              alt="Benedicto College Logo"
              class="h-8 sm:h-10 md:h-12 lg:h-14 w-auto max-w-full object-contain"
              onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
              onload="console.log('Logo loaded successfully:', this.src);"
            >
          </a>
        </div>

        <!-- Desktop Navigation - Right aligned with SVG icons only -->
        <nav class="hidden md:flex items-center space-x-1 lg:space-x-2 flex-shrink-0">
          <!-- Account/Login -->
          <div class="group relative">
            <a routerLink="/login" class="flex items-center justify-center p-2 rounded-lg transition-all duration-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
              Account
            </div>
          </div>

          <!-- About -->
          <div class="group relative">
            <a routerLink="/about" class="flex items-center justify-center p-2 rounded-lg transition-all duration-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
              About
            </div>
          </div>

          <!-- Support -->
          <div class="group relative">
            <a routerLink="/support" class="flex items-center justify-center p-2 rounded-lg transition-all duration-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
              Help & Support
            </div>
          </div>

          <!-- Contact -->
          <div class="group relative">
            <a routerLink="/contact" class="flex items-center justify-center p-2 rounded-lg transition-all duration-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </a>
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
              Contact
            </div>
          </div>
        </nav>

        <!-- Mobile menu button -->
        <button
          id="mobile-burger"
          (click)="toggleMobileMenu()"
          class="md:hidden flex items-center justify-center p-2 rounded-lg hover:bg-gray-800 transition-colors duration-300"
          aria-label="Toggle mobile menu"
        >
          <svg id="hamburger-icon" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          <svg id="close-icon" class="w-6 h-6 text-white absolute" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation Pane -->
    <nav id="nav-links" class="md:hidden">
      <div class="flex flex-col space-y-4 pt-8">
        <!-- Mobile Logo -->
        <div class="flex justify-center mb-6">
          <img src="assets/images/BcLogo.png" alt="Benedicto College" class="h-12 w-auto">
        </div>

        <!-- Mobile Navigation Links -->
        <a routerLink="/login" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Account
        </a>
        <a routerLink="/about" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          About
        </a>
        <a routerLink="/support" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Help & Support
        </a>
        <a routerLink="/contact" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Contact
        </a>
        <a routerLink="/terms-of-service" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Terms
        </a>
        <a routerLink="/privacy-policy" class="text-white text-center py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors duration-300">
          Privacy Policy
        </a>
      </div>
    </nav>
  </header>

  <!-- Main 404 Content -->
  <main class="flex-1 flex items-center justify-center px-4 py-16 bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="max-w-4xl mx-auto text-center">
      <!-- Benedicto College Logo -->
      <div class="mb-8 logo-fade-in">
        <img
          src="assets/images/BcLogo.png"
          alt="Benedicto College Logo"
          class="h-20 sm:h-24 md:h-28 w-auto mx-auto animate-bounce-slow"
          onerror="console.error('Logo failed to load:', this.src);"
        >
      </div>

      <!-- 404 Robot Animation -->
      <div class="mb-8 flex justify-center">
        <img
          src="assets/gif/404robot.gif"
          alt="404 Robot Animation"
          class="h-32 sm:h-40 md:h-48 w-auto mx-auto"
          onerror="console.error('404 Robot gif failed to load:', this.src);"
        >
      </div>

      <!-- 404 Error Message -->
      <div class="mb-12 error-slide-in">
        <h1 class="text-6xl sm:text-8xl md:text-9xl font-black text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text mb-6 leading-none">
          404
        </h1>
        <h2 class="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-800 mb-6">Page Not Found</h2>
        <div class="max-w-2xl mx-auto">
          <p class="text-xl sm:text-2xl text-gray-600 mb-4 leading-relaxed">
            Oops! The page you're looking for doesn't exist.
          </p>
          <p class="text-lg sm:text-xl text-gray-500 leading-relaxed">
            It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>
      </div>

      <!-- Benedicto College Context -->
      <div class="mb-12 p-8 bg-white rounded-2xl shadow-xl border border-gray-200 hover:shadow-2xl transition-all duration-300">
        <div class="flex items-center justify-center mb-4">
          <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
          </div>
        </div>
        <h3 class="text-2xl font-bold text-gray-800 mb-4">Benedicto College Library Management System</h3>
        <p class="text-gray-600 text-lg leading-relaxed">
          You're currently browsing the digital library portal for Benedicto College.
          Our system helps students, faculty, and staff access library resources efficiently.
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-6 justify-center button-group mb-16">
        <button
          (click)="goHome()"
          class="group px-10 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 hover:scale-105"
        >
          <div class="flex items-center justify-center">
            <svg class="w-6 h-6 mr-3 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            <span class="text-lg">Go to Homepage</span>
          </div>
        </button>

        <button
          (click)="goBack()"
          class="group px-10 py-4 bg-gradient-to-r from-gray-600 to-gray-700 text-white font-bold rounded-xl hover:from-gray-700 hover:to-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 hover:scale-105"
        >
          <div class="flex items-center justify-center">
            <svg class="w-6 h-6 mr-3 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            <span class="text-lg">Go Back</span>
          </div>
        </button>
      </div>

      <!-- Quick Links -->
      <div class="quick-links">
        <h4 class="text-2xl font-bold text-gray-800 mb-8">Quick Links</h4>
        <div class="grid grid-cols-2 sm:grid-cols-4 gap-6">
          <a routerLink="/login" class="group p-6 bg-white border border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-lg transition-all duration-300 text-center transform hover:-translate-y-1">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors duration-300">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-gray-700 group-hover:text-blue-600 transition-colors duration-300">Login</span>
          </a>

          <a routerLink="/about" class="group p-6 bg-white border border-gray-200 rounded-xl hover:border-purple-300 hover:shadow-lg transition-all duration-300 text-center transform hover:-translate-y-1">
            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors duration-300">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-gray-700 group-hover:text-purple-600 transition-colors duration-300">About</span>
          </a>

          <a routerLink="/support" class="group p-6 bg-white border border-gray-200 rounded-xl hover:border-green-300 hover:shadow-lg transition-all duration-300 text-center transform hover:-translate-y-1">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors duration-300">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-gray-700 group-hover:text-green-600 transition-colors duration-300">Support</span>
          </a>

          <a routerLink="/contact" class="group p-6 bg-white border border-gray-200 rounded-xl hover:border-pink-300 hover:shadow-lg transition-all duration-300 text-center transform hover:-translate-y-1">
            <div class="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-pink-200 transition-colors duration-300">
              <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <span class="text-sm font-semibold text-gray-700 group-hover:text-pink-600 transition-colors duration-300">Contact</span>
          </a>
        </div>
      </div>
    </div>
  </main>
</div>
